#===============================================================================
# Player Extension - Adds missing methods for Gen 4 Character Anims
#===============================================================================

class Game_Player < Game_Character
  #-----------------------------------------------------------------------------
  # Define the missing method that's causing the error
  #-----------------------------------------------------------------------------
  def check_event_trigger_after_turning
    # This method is intentionally empty to prevent the "undefined method" error
    # It would normally check for event triggers after the player turns
  end
end
