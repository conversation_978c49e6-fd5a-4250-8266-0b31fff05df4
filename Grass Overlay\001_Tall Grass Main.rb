class Sprite_TallGrass
  GRASS_TEXTURE_PATH = "Graphics/Plugins/Footset/grass"
  SPRITE_WIDTH = 64
  SPRITE_HEIGHT = 32
  POSITION_OFFSET_X = 0    # Changed from 1 to 0 for better horizontal alignment
  POSITION_OFFSET_Y = -2    # Changed from -2 to 8 to position grass at character's feet
  
  attr_reader :visible
  attr_accessor :event

  def initialize(sprite, event, viewport = nil)
    @rsprite = sprite
    @event = event
    @viewport = viewport
    @disposed = false
    @visible = true
    @tallgrassbitmap = AnimatedBitmap.new(GRASS_TEXTURE_PATH)
    @animation_playing = false
    @sprite = nil
    update
  end

  def dispose
    return if @disposed
    @sprite&.dispose
    @sprite = nil
    @disposed = true
  end

  def disposed?
    @disposed
  end

  def visible=(value)
    @visible = value
    update_visibility
  end

  def update
    return if should_skip_update?
    
    if should_dispose_sprite?
      dispose_sprite
      return
    end

    create_sprite_if_needed
    update_sprite_properties
  end

  private

  def should_skip_update?
    disposed? || !$scene || !$scene.is_a?(Scene_Map)
  end

  def should_dispose_sprite?
    @event.character_name.empty? || @event.character_name == "nil" || 
    @event.moving? || !valid_grass_position? || @event.transparent
  end

  def valid_grass_position?
    $game_map.terrain_tag(@event.x, @event.y) == GameData::TerrainTag.get(:Grass).id
  end

  def dispose_sprite
    if @sprite
      @sprite.dispose
      @sprite = nil
    end
  end

  def create_sprite_if_needed
    return if @sprite
    @sprite = Sprite.new(@viewport)
    @sprite.bitmap = @tallgrassbitmap.bitmap
    @sprite.visible = false
  end

  def update_sprite_properties
    @sprite.x = @rsprite.x + POSITION_OFFSET_X
    @sprite.y = @rsprite.y + POSITION_OFFSET_Y
    @sprite.ox = SPRITE_WIDTH / 2
    @sprite.oy = SPRITE_HEIGHT    # Changed from SPRITE_HEIGHT - 2 to SPRITE_HEIGHT
    @sprite.z = @rsprite.z + 1 
    @sprite.zoom_x = @rsprite.zoom_x
    @sprite.zoom_y = @rsprite.zoom_y
    @sprite.opacity = @rsprite.opacity
    pbDayNightTint(@sprite)
    @sprite.color.set(0, 0, 0, 0)
    update_visibility
  end

  def update_visibility
    return unless @sprite
    @sprite.visible = !@event.transparent && @visible && !@animation_playing
  end

  public

  def start_animation
    @animation_playing = true
    update_visibility
  end

  def end_animation
    @animation_playing = false
    update_visibility
  end
end

# Event Handler for Grass Animation
EventHandlers.add(:on_leave_tile, :grass_rustling,
  proc { |event|
    next if !$scene.is_a?(Scene_Map)
    event.each_occupied_tile do |x, y|
      next if !$map_factory.getTerrainTagFromCoords(event.map.map_id, x, y, true).shows_grass_rustle
      if spriteset = $scene.spriteset(event.map_id)
        spriteset.addUserAnimation(Settings::GRASS_ANIMATION_ID, x, y, true, 4)
      end
    end
  }
)

class Sprite_Character < RPG::Sprite
  alias initialize_with_shadows initialize
  def initialize(viewport, character = nil)
    initialize_with_shadows(viewport, character)
    @tallgrass = Sprite_TallGrass.new(self, character, viewport)
  end

  alias dispose_with_shadows dispose
  def dispose
    @tallgrass&.dispose
    dispose_with_shadows
  end

  alias update_with_shadows update
  def update
    update_with_shadows
    @tallgrass&.update
  end

  alias visible_with_shadows= visible=
  def visible=(value)
    self.visible_with_shadows = value
    @tallgrass&.visible = value
  end
end
